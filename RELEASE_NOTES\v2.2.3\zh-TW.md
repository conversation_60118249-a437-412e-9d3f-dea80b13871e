# Release v2.2.3 - 超時控制與圖片設定增強

## 🌟 亮點
本版本新增了用戶可控制的超時設定功能，以及靈活的圖片上傳設定選項，同時完善了 UV Cache 管理工具，提升整體使用體驗。

## ✨ 新功能
- ⏰ **用戶超時控制**: 新增可自訂的超時設定功能，支援 30 秒至 2 小時的彈性設定
- ⏱️ **倒數計時器**: 介面頂部顯示即時倒數計時器，提供視覺化的時間提醒
- 🖼️ **圖片大小限制**: 新增圖片上傳大小限制設定（無限制/1MB/3MB/5MB）
- 🔧 **Base64 相容模式**: 新增 Base64 詳細模式，提升與 Gemini 等 AI 模型的圖片識別相容性
- 🧹 **UV Cache 管理工具**: 新增 `cleanup_cache.py` 腳本，協助管理和清理 UV cache 空間

## 🚀 改進功能
- 📚 **文檔結構優化**: 重新整理文檔目錄結構，將圖片移至 `docs/{語言}/images/` 路徑
- 📖 **Cache 管理指南**: 新增詳細的 UV Cache 管理指南，包含自動化清理方案
- 🎯 **智能相容性提示**: 當圖片上傳失敗時自動顯示 Base64 相容模式建議
- 🔄 **設定同步機制**: 改進圖片設定在不同介面模式間的同步機制

## 🐛 問題修復
- 🛡️ **超時處理優化**: 改進用戶自訂超時與 MCP 系統超時的協調機制
- 🖥️ **介面自動關閉**: 修復超時後介面自動關閉和資源清理邏輯
- 📱 **響應式佈局**: 優化超時控制元件在小螢幕設備上的顯示效果

## 🔧 技術改進
- 🎛️ **超時控制架構**: 實現前端倒數計時器與後端超時處理的分離設計
- 📊 **圖片處理優化**: 改進圖片上傳的大小檢查和格式驗證機制
- 🗂️ **設定持久化**: 增強設定保存機制，確保用戶偏好的正確保存和載入
- 🧰 **工具腳本增強**: 新增跨平台的 cache 清理工具，支援強制清理和預覽模式

## 📦 安裝與更新
```bash
# 快速測試最新版本
uvx mcp-feedback-enhanced@latest test --gui

# 更新到特定版本
uvx mcp-feedback-enhanced@v2.2.3 test
```

## 🔗 相關連結
- 完整文檔: [README.zh-TW.md](../../README.zh-TW.md)
- 問題回報: [GitHub Issues](https://github.com/Minidoracat/mcp-feedback-enhanced/issues)
- 相關 PR: #22 (超時控制功能), #19 (圖片設定功能)
