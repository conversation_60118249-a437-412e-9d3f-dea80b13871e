# 更新日誌 (繁體中文)

本文件記錄了 **MCP Feedback Enhanced** 的所有版本更新內容。

---
# Release v2.2.3 - 超時控制與圖片設定增強

## 🌟 亮點
本版本新增了用戶可控制的超時設定功能，以及靈活的圖片上傳設定選項，同時完善了 UV Cache 管理工具，提升整體使用體驗。

## ✨ 新功能
- ⏰ **用戶超時控制**: 新增可自訂的超時設定功能，支援 30 秒至 2 小時的彈性設定
- ⏱️ **倒數計時器**: 介面頂部顯示即時倒數計時器，提供視覺化的時間提醒
- 🖼️ **圖片大小限制**: 新增圖片上傳大小限制設定（無限制/1MB/3MB/5MB）
- 🔧 **Base64 相容模式**: 新增 Base64 詳細模式，提升與 Gemini 等 AI 模型的圖片識別相容性
- 🧹 **UV Cache 管理工具**: 新增 `cleanup_cache.py` 腳本，協助管理和清理 UV cache 空間

## 🚀 改進功能
- 📚 **文檔結構優化**: 重新整理文檔目錄結構，將圖片移至 `docs/{語言}/images/` 路徑
- 📖 **Cache 管理指南**: 新增詳細的 UV Cache 管理指南，包含自動化清理方案
- 🎯 **智能相容性提示**: 當圖片上傳失敗時自動顯示 Base64 相容模式建議
- 🔄 **設定同步機制**: 改進圖片設定在不同介面模式間的同步機制

## 🐛 問題修復
- 🛡️ **超時處理優化**: 改進用戶自訂超時與 MCP 系統超時的協調機制
- 🖥️ **介面自動關閉**: 修復超時後介面自動關閉和資源清理邏輯
- 📱 **響應式佈局**: 優化超時控制元件在小螢幕設備上的顯示效果

## 🔧 技術改進
- 🎛️ **超時控制架構**: 實現前端倒數計時器與後端超時處理的分離設計
- 📊 **圖片處理優化**: 改進圖片上傳的大小檢查和格式驗證機制
- 🗂️ **設定持久化**: 增強設定保存機制，確保用戶偏好的正確保存和載入
- 🧰 **工具腳本增強**: 新增跨平台的 cache 清理工具，支援強制清理和預覽模式

## 📦 安裝與更新
```bash
# 快速測試最新版本
uvx mcp-feedback-enhanced@latest test --gui

# 更新到特定版本
uvx mcp-feedback-enhanced@v2.2.3 test
```

## 🔗 相關連結
- 完整文檔: [README.zh-TW.md](../../README.zh-TW.md)
- 問題回報: [GitHub Issues](https://github.com/Minidoracat/mcp-feedback-enhanced/issues)
- 相關 PR: #22 (超時控制功能), #19 (圖片設定功能)

---

## [v2.2.3] - 超時控制與圖片設定增強 (2025-01-XX)

### 🌟 亮點
本版本新增了用戶可控制的超時設定功能，以及靈活的圖片上傳設定選項，同時完善了 UV Cache 管理工具，提升整體使用體驗。

### ✨ 新功能
- ⏰ **用戶超時控制**: 新增可自訂的超時設定功能，支援 30 秒至 2 小時的彈性設定
- ⏱️ **倒數計時器**: 介面頂部顯示即時倒數計時器，提供視覺化的時間提醒
- 🖼️ **圖片大小限制**: 新增圖片上傳大小限制設定（無限制/1MB/3MB/5MB）
- 🔧 **Base64 相容模式**: 新增 Base64 詳細模式，提升與 Gemini 等 AI 模型的圖片識別相容性
- 🧹 **UV Cache 管理工具**: 新增 `cleanup_cache.py` 腳本，協助管理和清理 UV cache 空間

### 🚀 改進功能
- 📚 **文檔結構優化**: 重新整理文檔目錄結構，將圖片移至 `docs/{語言}/images/` 路徑
- 📖 **Cache 管理指南**: 新增詳細的 UV Cache 管理指南，包含自動化清理方案
- 🎯 **智能相容性提示**: 當圖片上傳失敗時自動顯示 Base64 相容模式建議
- 🔄 **設定同步機制**: 改進圖片設定在不同介面模式間的同步機制

### 🐛 問題修復
- 🛡️ **超時處理優化**: 改進用戶自訂超時與 MCP 系統超時的協調機制
- 🖥️ **介面自動關閉**: 修復超時後介面自動關閉和資源清理邏輯
- 📱 **響應式佈局**: 優化超時控制元件在小螢幕設備上的顯示效果

### 🔧 技術改進
- 🎛️ **超時控制架構**: 實現前端倒數計時器與後端超時處理的分離設計
- 📊 **圖片處理優化**: 改進圖片上傳的大小檢查和格式驗證機制
- 🗂️ **設定持久化**: 增強設定保存機制，確保用戶偏好的正確保存和載入
- 🧰 **工具腳本增強**: 新增跨平台的 cache 清理工具，支援強制清理和預覽模式

---

## [v2.2.2] - 超時自動清理修復 (2024-12-XX)

### 🌟 亮點
本版本修復了一個重要的資源管理問題：當 MCP session 因超時結束時，GUI/Web UI 介面沒有正確關閉，導致介面持續顯示而無法正常關閉。

### 🐛 問題修復
- 🔄 **超時自動清理**: 修復 GUI/Web UI 在 MCP session timeout (預設 600 秒) 後沒有自動關閉的問題 (fixes #5)
- 🛡️ **資源管理優化**: 改進超時處理機制，確保在超時時正確清理和關閉所有 UI 資源
- ⚡ **超時檢測增強**: 加強超時檢測邏輯，確保在各種情況下都能正確處理超時事件
- 🔧 **介面回應改進**: 改善 Web UI 前端對 session timeout 事件的處理回應

### 🚀 技術改進
- 📦 **Web Session 管理**: 重構 WebFeedbackSession 的超時處理邏輯
- 🎯 **QTimer 整合**: 在 GUI 中引入精確的 QTimer 超時控制機制
- 🌐 **前端通訊優化**: 改進 Web UI 前端與後端的超時訊息傳遞
- 🧹 **資源清理機制**: 新增 _cleanup_resources_on_timeout 方法確保徹底清理

---

## [v2.2.1] - 視窗優化與統一設定接口 (2024-12-XX)

### 🌟 亮點
本版本主要解決了 GUI 視窗大小限制問題，實現了視窗狀態的智能保存機制，並優化了設定接口的統一性。

### 🚀 改進功能
- 🖥️ **視窗大小限制解除**: 解除 GUI 主視窗最小大小限制，從 1000×800 降至 400×300，讓用戶可以自由調整視窗大小以符合不同使用場景
- 💾 **視窗狀態實時保存**: 實現視窗大小與位置的即時保存機制，支援防抖延遲避免過度頻繁的 I/O 操作
- ⚙️ **統一設定接口優化**: 改進 GUI 設定版面的配置保存邏輯，避免設定衝突，確保視窗定位與大小設定的正確性
- 🎯 **智能視窗大小保存**: 「總是在主螢幕中心顯示」模式下正確保存視窗大小（但不保存位置），「智能定位」模式下保存完整的視窗狀態

### 🐛 問題修復
- 🔧 **視窗大小限制**: 解決 GUI 視窗無法調整至小尺寸的問題 (fixes #10 第一部分)
- 🛡️ **設定衝突**: 修復設定保存時可能出現的配置衝突問題

---

## [v2.2.0] - 佈局與設定界面優化 (2024-12-XX)

### 🌟 亮點
本版本新增了水平佈局選項，優化了設定界面，並修復了快捷鍵和圖片貼上問題。

### ✨ 新功能
- 🎨 **水平佈局模式**: GUI 與 Web UI 的合併模式新增摘要與回饋的左右佈局（水平分割）選項，提供更靈活的檢視方式 (實現 [Issue #1](https://github.com/Minidoracat/mcp-feedback-enhanced/issues/1))

### 🚀 改進功能
- 🎨 **設定界面改進**: 優化了 GUI 與 Web UI 的設定頁面，提升佈局清晰度與用戶操作體驗
- ⌨️ **快捷鍵完善 (GUI)**: 提交回饋快捷鍵 (Ctrl+Enter / Cmd+Enter) 現已完整支援數字鍵盤(九宮格)的 Enter 鍵

### 🐛 問題修復
- 🔧 **圖片重複貼上 (Web UI)**: 解決了在文字輸入區使用 Ctrl+V 貼上圖片時，可能導致圖片重複貼上的問題

---

## [v2.1.1] - 視窗定位優化 (2024-11-XX)

### 🌟 亮點
完美解決多螢幕環境下的視窗定位問題，特別是 T 字型螢幕排列等複雜配置。

### ✨ 新功能
- 🖥️ **智能視窗定位**: 新增「總是在主螢幕中心顯示視窗」設定選項
- 🌐 **多螢幕支援**: 完美解決 T 字型螢幕排列等複雜多螢幕環境的視窗定位問題
- 💾 **位置記憶**: 自動保存和恢復視窗位置，智能檢測視窗可見性
- ⚙️ **用戶選擇**: 提供智能定位（預設）和強制中心顯示兩種模式

---

## [v2.1.0] - 全面重構版 (2024-11-XX)

### 🌟 亮點
這是一個重大重構版本，GUI 和 Web UI 均採用了全新的模組化架構。

### 🎨 重大重構
- 🏗️ **全面重構**: GUI 和 Web UI 採用模組化架構
- 📁 **集中管理**: 重新組織資料夾結構，提升維護性
- 🖥️ **界面優化**: 現代化設計和改進的用戶體驗

### ✨ 新功能
- 🍎 **macOS 界面優化**: 針對 macOS 用戶體驗進行專項改進
- ⚙️ **功能增強**: 新增設定選項和自動關閉頁面功能
- ℹ️ **關於頁面**: 新增關於頁面，包含版本資訊、專案連結和致謝內容

### 🐛 問題修復
- 🌐 **語言切換**: 修復 Web UI 語言切換時內容更新問題

---

## [v2.0.14] - 快捷鍵與圖片功能增強 (2024-10-XX)

### 🚀 改進功能
- ⌨️ **增強快捷鍵**: Ctrl+Enter 支援數字鍵盤
- 🖼️ **智能圖片貼上**: Ctrl+V 直接貼上剪貼板圖片

---

## [v2.0.9] - 多語言架構重構 (2024-10-XX)

### 🔄 重構
- 🌏 **多語言架構重構**: 支援動態載入
- 📁 **語言檔案模組化**: 模組化組織語言檔案

---

## [v2.0.3] - 編碼問題修復 (2024-10-XX)

### 🐛 重要修復
- 🛡️ **完全修復中文字符編碼問題**: 解決所有中文顯示相關問題
- 🔧 **解決 JSON 解析錯誤**: 修復資料解析錯誤

---

## [v2.0.0] - Web UI 支援 (2024-09-XX)

### 🌟 重大功能
- ✅ **新增 Web UI 支援**: 支援遠端環境使用
- ✅ **自動環境檢測**: 自動選擇合適的界面
- ✅ **WebSocket 即時通訊**: 實現即時雙向通訊

---

## 圖例說明

| 圖示 | 意義 |
|------|------|
| 🌟 | 版本亮點 |
| ✨ | 新功能 |
| 🚀 | 改進功能 |
| 🐛 | 問題修復 |
| 🔄 | 重構變更 |
| 🎨 | 界面優化 |
| ⚙️ | 設定相關 |
| 🖥️ | 視窗相關 |
| 🌐 | 多語言/網路相關 |
| 📁 | 檔案結構 |
| ⌨️ | 快捷鍵 |
| 🖼️ | 圖片功能 |

---

**完整專案資訊：** [GitHub - mcp-feedback-enhanced](https://github.com/Minidoracat/mcp-feedback-enhanced) 